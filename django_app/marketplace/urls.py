from django.urls import path
from . import views

urlpatterns = [
    path('', views.home, name='home'),

    path('profile/', views.profile_view, name='profile'),
    path('profile/edit/', views.edit_profile, name='edit_profile'),

    path('services/', views.service_list, name='service_list'),
    path('services/create/', views.create_service, name='create_service'),
    path('services/<int:service_id>/', views.service_detail, name='service_detail'),
    path('services/<int:service_id>/edit/', views.edit_service, name='edit_service'),

    path('signup/', views.signup_view, name='signup'),

    path('bookings/', views.booking_list, name='booking_list'),
    path('services/<int:service_id>/book/', views.create_booking, name='create_booking'),

    path('reviews/', views.review_list, name='review_list'),
    path('bookings/<int:booking_id>/review/', views.create_review, name='create_review'),
]
