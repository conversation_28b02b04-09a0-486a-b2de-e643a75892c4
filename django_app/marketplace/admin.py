from django.contrib import admin
from .models import Profile, Service, Booking, Review

@admin.register(Profile)
class ProfileAdmin(admin.ModelAdmin):
    list_display = ('user', 'bio')
    search_fields = ('user__username',)

@admin.register(Service)
class ServiceAdmin(admin.ModelAdmin):
    list_display = ('title', 'freelancer', 'price', 'category')
    list_filter = ('category',)
    search_fields = ('title', 'freelancer__user__username')

@admin.register(Booking)
class BookingAdmin(admin.ModelAdmin):
    list_display = ('service', 'client', 'status', 'booking_date')
    list_filter = ('status',)
    search_fields = ('service__title', 'client__username')

@admin.register(Review)
class ReviewAdmin(admin.ModelAdmin):
    list_display = ('booking', 'rating', 'created_at')
    search_fields = ('booking__service__title',)
