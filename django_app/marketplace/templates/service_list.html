{% extends 'base.html' %}
{% block content %}
<style>
    .card-img-top {
        width: auto;
        height: 300px;
    }
    .card img {
        object-fit: cover;
        object-position: center;
    }
</style>
<div class="container mt-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2>Available services</h2>

        {% if user.is_authenticated %}
        <a href="{% url 'create_service' %}" class="btn btn-success">Post a service</a>
        {% endif %}
    </div>
    <form method="GET" class="row g-3 mb-4">
        <div class="col-md-4">
            <input type="text" name="search" placeholder="Keyword" class="form-control" value="{{ search }}">
        </div>
        <div class="col-md-4">
            <input type="text" name="category" placeholder="category" class="form-control" value="{{ category }}">
        </div>
        <div class="col-md-4">
            <button type="submit" class="btn btn-primary w-100">Filter</button>
        </div>
    </form>
    <div class="row">
        {% for service in services %}
        <div class="col-md-4 mb-4">
            <div class="card h-100">
                {% if service.service_image %}
                <img src="{{ service.service_image.url }}" class="card-img-top" alt="Image du service">
                {% else %}
                <img src="https://via.placeholder.com/300x200" class="card-img-top" alt="Image manquante">
                {% endif %}
                <div class="card-body">
                    <h5 class="card-title">{{ service.title }}</h5>
                    <p class="card-text">{{ service.description|truncatewords:20 }}</p>
                    <p class="fw-bold">Category : {{ service.category }}</p>
                    <p class="fw-bold">Price : {{ service.price }} $</p>
                    <a href="{% url 'service_detail' service.id %}" class="btn btn-primary">See more</a>
                </div>
            </div>
        </div>
        {% empty %}
        <p>No services available at this time.</p>
        {% endfor %}
    </div>
</div>
{% endblock %}
