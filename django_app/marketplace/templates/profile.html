{% extends 'base.html' %}
{% block content %}
<style>
    .card-body img {
        object-fit: cover;
    }
</style>
<div class="container mt-5">
    <h2 class="mb-4">Mon Profil</h2>
    <div class="card" style="max-width: 600px;">
        <div class="card-body text-center">
            {% if profile.profile_image %}
                <img src="{{ profile.profile_image.url }}" class="rounded-circle mb-3" width="150" height="150" alt="Profile Image">
            {% else %}
                <img src="https://via.placeholder.com/150" class="rounded-circle mb-3" alt="Default Image">
            {% endif %}
            <h4>{{ profile.user.username }}</h4>
            <p class="text-muted">{{ profile.user.email }}</p>
            <p>{{ profile.bio|default:"No bio available" }}</p>
            <a href="{% url 'edit_profile' %}" class="btn btn-primary mt-2">Edit my profile</a>
        </div>
    </div>
</div>
{% endblock %}
