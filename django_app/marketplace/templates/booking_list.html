{% extends "base.html" %}
{% block title %}My reservations{% endblock %}
{% block content %}
<h2>My reservations</h2>
{% if bookings %}
  <table class="table table-striped">
    <thead>
      <tr>
        <th>Service</th>
        <th>Client</th>
        <th>Date</th>
        <th>Status</th>
        <th>Actions</th>
      </tr>
    </thead>
    <tbody>
      {% for booking in bookings %}
        <tr>
          <td>{{ booking.service.title }}</td>
          <td>{{ booking.client.username }}</td>
          <td>{{ booking.booking_date|date:"d/m/Y H:i" }}</td>
          <td>{{ booking.status }}</td>
          <td>
            {% if user == booking.client and not booking.review %}
              <a href="{% url 'create_review' booking.id %}" class="btn btn-sm btn-primary">Leave a review</a>
            {% else %}
              -
            {% endif %}
          </td>
        </tr>
      {% endfor %}
    </tbody>
  </table>
{% else %}
  <p>You currently have no reservations.</p>
{% endif %}
{% endblock %}
