<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <title>{% block title %}Freelance Marketplace{% endblock %}</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet" />
</head>
<style>
  .navbar-collapse ul {
    display: flex;
    flex-direction: row;
    align-items: center;
  }
</style>
<body>
<nav class="navbar navbar-expand-lg navbar-dark bg-primary ">
  <div class="container">
    <a class="navbar-brand" href="{% url 'home' %}">Freelance Marketplace</a>
    <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
      <span class="navbar-toggler-icon"></span>
    </button>
    <div class="collapse navbar-collapse" id="navbarNav">
      <ul class="navbar-nav me-auto">
        <li class="nav-item"><a class="nav-link" href="{% url 'service_list' %}">Services</a></li>
        {% if user.is_authenticated %}
          <li class="nav-item"><a class="nav-link" href="{% url 'profile' %}">Profile</a></li>
          <li class="nav-item"><a class="nav-link" href="{% url 'booking_list' %}">Réservations</a></li>
        {% endif %}
      </ul>
      <ul class="navbar-nav ms-auto">
        {% if user.is_authenticated %}
          <li class="nav-item">
            <span class="navbar-text me-3">Hello, {{ user.username }}</span>
          </li>
          <li class="nav-item">
            <form method="post" action="{% url 'logout' %}">
              {% csrf_token %}
              <button type="submit" class="btn btn-link nav-link" style="padding: 0;">Logout</button>
            </form>
          </li>
        {% else %}
          <li class="nav-item"><a class="nav-link" href="{% url 'login' %}">Login</a></li>
          <li class="nav-item"><a class="nav-link" href="{% url 'signup' %}">Sign Up</a></li>
        {% endif %}
      </ul>
    </div>
  </div>
</nav>
<div class="container-fluid px-0">
  {% if messages %}
    {% for message in messages %}
      <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
        {{ message }}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
      </div>
    {% endfor %}
  {% endif %}
  {% block content %}{% endblock %}
</div>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
