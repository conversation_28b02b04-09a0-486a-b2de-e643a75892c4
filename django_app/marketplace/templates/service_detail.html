{% extends 'base.html' %}

{% block content %}
<style>
    .img-fluid  {
        height: 500px;
    }
</style>
<div class="container mt-5">
    <h2>{{ service.title }}</h2>

    {% if service.service_image %}
        <img src="{{ service.service_image.url }}" class="img-fluid mb-3" alt="Image service">
    {% endif %}

    <p><strong>Freelancer :</strong> {{ service.freelancer.user.username }}</p>
    <p><strong>Category :</strong> {{ service.category }}</p>
    <p><strong>Description :</strong> {{ service.description }}</p>
    <p><strong>Price :</strong> {{ service.price }} $ </p>

    {% if user.is_authenticated %}
        <a href="{% url 'create_booking' service.id %}" class="btn btn-primary mt-3">Book this service</a>
    {% else %}
        <p class="mt-3">Log in to book this service.</p>
    {% endif %}
        {% if user.is_authenticated and service.freelancer.user == user %}
    <a href="{% url 'edit_service' service.id %}" class="btn btn-warning mt-3">Edit this service</a>
{% endif %}
    <hr class="my-5">
    <h3>Customer reviews</h3>
    {% if reviews %}
        {% for review in reviews %}
            <div class="card mb-3">
                <div class="card-body">
                    <h5 class="card-title">Rate : {{ review.rating }}/5</h5>
                    <p class="card-text">{{ review.comment }}</p>
                    <small class="text-muted">
                        Posted by {{ review.booking.client.username }} the {{ review.created_at|date:"d M Y à H:i" }}
                    </small>
                </div>
            </div>
        {% endfor %}
    {% else %}
        <p>No reviews for this service at the moment.</p>
    {% endif %}
</div>
{% endblock %}
