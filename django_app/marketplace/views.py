from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.forms import UserCreationForm
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from .models import Profile, Service, Booking, Review
from .forms import ProfileForm, ServiceForm, ReviewForm
from django.contrib.auth.models import User
from .forms import ProfileForm

def home(request):
    return render(request, 'home.html')

@login_required
def profile_view(request):
    profile, created = Profile.objects.get_or_create(user=request.user)
    if request.method == 'POST':
        form = ProfileForm(request.POST, request.FILES, instance=profile)
        if form.is_valid():
            form.save()
            messages.success(request, "Profile successfully updated.")
            return redirect('profile_view')
    else:
        form = ProfileForm(instance=profile)
    return render(request, 'profile.html', {'form': form})

@login_required
def service_list(request):
    services = Service.objects.all().order_by('-created_at')
    return render(request, 'service_list.html', {'services': services})

@login_required
def create_service(request):
    profile = get_object_or_404(Profile, user=request.user)
    if request.method == 'POST':
        form = ServiceForm(request.POST, request.FILES)
        if form.is_valid():
            service = form.save(commit=False)
            service.freelancer = profile
            service.save()
            messages.success(request, "Service successfully created.")
            return redirect('service_list')
    else:
        form = ServiceForm()
    return render(request, 'service_form.html', {'form': form})

@login_required
def booking_list(request):
    bookings = Booking.objects.filter(client=request.user).order_by('-booking_date')
    return render(request, 'booking_list.html', {'bookings': bookings})

@login_required
def create_booking(request, service_id):
    service = get_object_or_404(Service, id=service_id)
    if request.method == 'POST':
        booking = Booking.objects.create(service=service, client=request.user)
        messages.success(request, f"Service booking '{service.title}' Done.")
        return redirect('booking_list')
    return render(request, 'booking_form.html', {'service': service})

@login_required
def review_list(request):
    reviews = Review.objects.filter(booking__client=request.user).order_by('-created_at')
    return render(request, 'review_list.html', {'reviews': reviews})

@login_required
def create_review(request, booking_id):
    booking = get_object_or_404(Booking, id=booking_id, client=request.user)
    if hasattr(booking, 'review'):
        messages.info(request, "You have already left a review for this reservation.")
        return redirect('review_list')
    if request.method == 'POST':
        form = ReviewForm(request.POST)
        if form.is_valid():
            review = form.save(commit=False)
            review.booking = booking
            review.save()
            messages.success(request, "Notice sent successfully.")
            return redirect('review_list')
    else:
        form = ReviewForm()
    return render(request, 'review_form.html', {'form': form, 'booking': booking})

@login_required
def profile_view(request):
    profile, created = Profile.objects.get_or_create(user=request.user)
    return render(request, 'profile.html', {'profile': profile})
@login_required
def edit_profile(request):
    profile = Profile.objects.get(user=request.user)
    if request.method == 'POST':
        form = ProfileForm(request.POST, request.FILES, instance=profile)
        if form.is_valid():
            form.save()
            return redirect('profile')
    else:
        form = ProfileForm(instance=profile)
    return render(request, 'edit_profile.html', {'form': form})

def service_list(request):
    search = request.GET.get('search', '')
    category = request.GET.get('category', '')

    services = Service.objects.all()

    if search:
        services = services.filter(title__icontains=search) | services.filter(description__icontains=search)

    if category:
        services = services.filter(category__icontains=category)

    context = {
        'services': services,
        'search': search,
        'category': category,
    }
    return render(request, 'service_list.html', context)
def signup_view(request):
    if request.method == 'POST':
        form = UserCreationForm(request.POST)
        if form.is_valid():
            form.save()
            return redirect('login')  
    else:
        form = UserCreationForm()
    return render(request, 'registration/signup.html', {'form': form})
def service_detail(request, service_id):
    service = get_object_or_404(Service, id=service_id)

    reviews = Review.objects.filter(booking__service=service)

    return render(request, 'service_detail.html', {
        'service': service,
        'reviews': reviews,
    })

@login_required
def edit_service(request, service_id):
    service = get_object_or_404(Service, id=service_id)

    if service.freelancer.user != request.user:
        return redirect('service_list') 

    if request.method == 'POST':
        form = ServiceForm(request.POST, request.FILES, instance=service)
        if form.is_valid():
            form.save()
            return redirect('service_detail', service_id=service.id)
    else:
        form = ServiceForm(instance=service)

    return render(request, 'edit_service.html', {'form': form, 'service': service})