######################## BEGIN LICENSE BLOCK ########################
# The Original Code is Mozilla Communicator client code.
#
# The Initial Developer of the Original Code is
# Netscape Communications Corporation.
# Portions created by the Initial Developer are Copyright (C) 1998
# the Initial Developer. All Rights Reserved.
#
# Contributor(s):
#   Mark Pilgrim - port to Python
#
# This library is free software; you can redistribute it and/or
# modify it under the terms of the GNU Lesser General Public
# License as published by the Free Software Foundation; either
# version 2.1 of the License, or (at your option) any later version.
#
# This library is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the GNU
# Lesser General Public License for more details.
#
# You should have received a copy of the GNU Lesser General Public
# License along with this library; if not, write to the Free Software
# Foundation, Inc., 51 Franklin St, Fifth Floor, Boston, MA
# 02110-1301  USA
######################### END LICENSE BLOCK #########################

# The frequency data itself is the same as euc-kr.
# This is just a mapping table to euc-kr.

JOHAB_TO_EUCKR_ORDER_TABLE = {
    0x8861: 0,
    0x8862: 1,
    0x8865: 2,
    0x8868: 3,
    0x8869: 4,
    0x886A: 5,
    0x886B: 6,
    0x8871: 7,
    0x8873: 8,
    0x8874: 9,
    0x8875: 10,
    0x8876: 11,
    0x8877: 12,
    0x8878: 13,
    0x8879: 14,
    0x887B: 15,
    0x887C: 16,
    0x887D: 17,
    0x8881: 18,
    0x8882: 19,
    0x8885: 20,
    0x8889: 21,
    0x8891: 22,
    0x8893: 23,
    0x8895: 24,
    0x8896: 25,
    0x8897: 26,
    0x88A1: 27,
    0x88A2: 28,
    0x88A5: 29,
    0x88A9: 30,
    0x88B5: 31,
    0x88B7: 32,
    0x88C1: 33,
    0x88C5: 34,
    0x88C9: 35,
    0x88E1: 36,
    0x88E2: 37,
    0x88E5: 38,
    0x88E8: 39,
    0x88E9: 40,
    0x88EB: 41,
    0x88F1: 42,
    0x88F3: 43,
    0x88F5: 44,
    0x88F6: 45,
    0x88F7: 46,
    0x88F8: 47,
    0x88FB: 48,
    0x88FC: 49,
    0x88FD: 50,
    0x8941: 51,
    0x8945: 52,
    0x8949: 53,
    0x8951: 54,
    0x8953: 55,
    0x8955: 56,
    0x8956: 57,
    0x8957: 58,
    0x8961: 59,
    0x8962: 60,
    0x8963: 61,
    0x8965: 62,
    0x8968: 63,
    0x8969: 64,
    0x8971: 65,
    0x8973: 66,
    0x8975: 67,
    0x8976: 68,
    0x8977: 69,
    0x897B: 70,
    0x8981: 71,
    0x8985: 72,
    0x8989: 73,
    0x8993: 74,
    0x8995: 75,
    0x89A1: 76,
    0x89A2: 77,
    0x89A5: 78,
    0x89A8: 79,
    0x89A9: 80,
    0x89AB: 81,
    0x89AD: 82,
    0x89B0: 83,
    0x89B1: 84,
    0x89B3: 85,
    0x89B5: 86,
    0x89B7: 87,
    0x89B8: 88,
    0x89C1: 89,
    0x89C2: 90,
    0x89C5: 91,
    0x89C9: 92,
    0x89CB: 93,
    0x89D1: 94,
    0x89D3: 95,
    0x89D5: 96,
    0x89D7: 97,
    0x89E1: 98,
    0x89E5: 99,
    0x89E9: 100,
    0x89F3: 101,
    0x89F6: 102,
    0x89F7: 103,
    0x8A41: 104,
    0x8A42: 105,
    0x8A45: 106,
    0x8A49: 107,
    0x8A51: 108,
    0x8A53: 109,
    0x8A55: 110,
    0x8A57: 111,
    0x8A61: 112,
    0x8A65: 113,
    0x8A69: 114,
    0x8A73: 115,
    0x8A75: 116,
    0x8A81: 117,
    0x8A82: 118,
    0x8A85: 119,
    0x8A88: 120,
    0x8A89: 121,
    0x8A8A: 122,
    0x8A8B: 123,
    0x8A90: 124,
    0x8A91: 125,
    0x8A93: 126,
    0x8A95: 127,
    0x8A97: 128,
    0x8A98: 129,
    0x8AA1: 130,
    0x8AA2: 131,
    0x8AA5: 132,
    0x8AA9: 133,
    0x8AB6: 134,
    0x8AB7: 135,
    0x8AC1: 136,
    0x8AD5: 137,
    0x8AE1: 138,
    0x8AE2: 139,
    0x8AE5: 140,
    0x8AE9: 141,
    0x8AF1: 142,
    0x8AF3: 143,
    0x8AF5: 144,
    0x8B41: 145,
    0x8B45: 146,
    0x8B49: 147,
    0x8B61: 148,
    0x8B62: 149,
    0x8B65: 150,
    0x8B68: 151,
    0x8B69: 152,
    0x8B6A: 153,
    0x8B71: 154,
    0x8B73: 155,
    0x8B75: 156,
    0x8B77: 157,
    0x8B81: 158,
    0x8BA1: 159,
    0x8BA2: 160,
    0x8BA5: 161,
    0x8BA8: 162,
    0x8BA9: 163,
    0x8BAB: 164,
    0x8BB1: 165,
    0x8BB3: 166,
    0x8BB5: 167,
    0x8BB7: 168,
    0x8BB8: 169,
    0x8BBC: 170,
    0x8C61: 171,
    0x8C62: 172,
    0x8C63: 173,
    0x8C65: 174,
    0x8C69: 175,
    0x8C6B: 176,
    0x8C71: 177,
    0x8C73: 178,
    0x8C75: 179,
    0x8C76: 180,
    0x8C77: 181,
    0x8C7B: 182,
    0x8C81: 183,
    0x8C82: 184,
    0x8C85: 185,
    0x8C89: 186,
    0x8C91: 187,
    0x8C93: 188,
    0x8C95: 189,
    0x8C96: 190,
    0x8C97: 191,
    0x8CA1: 192,
    0x8CA2: 193,
    0x8CA9: 194,
    0x8CE1: 195,
    0x8CE2: 196,
    0x8CE3: 197,
    0x8CE5: 198,
    0x8CE9: 199,
    0x8CF1: 200,
    0x8CF3: 201,
    0x8CF5: 202,
    0x8CF6: 203,
    0x8CF7: 204,
    0x8D41: 205,
    0x8D42: 206,
    0x8D45: 207,
    0x8D51: 208,
    0x8D55: 209,
    0x8D57: 210,
    0x8D61: 211,
    0x8D65: 212,
    0x8D69: 213,
    0x8D75: 214,
    0x8D76: 215,
    0x8D7B: 216,
    0x8D81: 217,
    0x8DA1: 218,
    0x8DA2: 219,
    0x8DA5: 220,
    0x8DA7: 221,
    0x8DA9: 222,
    0x8DB1: 223,
    0x8DB3: 224,
    0x8DB5: 225,
    0x8DB7: 226,
    0x8DB8: 227,
    0x8DB9: 228,
    0x8DC1: 229,
    0x8DC2: 230,
    0x8DC9: 231,
    0x8DD6: 232,
    0x8DD7: 233,
    0x8DE1: 234,
    0x8DE2: 235,
    0x8DF7: 236,
    0x8E41: 237,
    0x8E45: 238,
    0x8E49: 239,
    0x8E51: 240,
    0x8E53: 241,
    0x8E57: 242,
    0x8E61: 243,
    0x8E81: 244,
    0x8E82: 245,
    0x8E85: 246,
    0x8E89: 247,
    0x8E90: 248,
    0x8E91: 249,
    0x8E93: 250,
    0x8E95: 251,
    0x8E97: 252,
    0x8E98: 253,
    0x8EA1: 254,
    0x8EA9: 255,
    0x8EB6: 256,
    0x8EB7: 257,
    0x8EC1: 258,
    0x8EC2: 259,
    0x8EC5: 260,
    0x8EC9: 261,
    0x8ED1: 262,
    0x8ED3: 263,
    0x8ED6: 264,
    0x8EE1: 265,
    0x8EE5: 266,
    0x8EE9: 267,
    0x8EF1: 268,
    0x8EF3: 269,
    0x8F41: 270,
    0x8F61: 271,
    0x8F62: 272,
    0x8F65: 273,
    0x8F67: 274,
    0x8F69: 275,
    0x8F6B: 276,
    0x8F70: 277,
    0x8F71: 278,
    0x8F73: 279,
    0x8F75: 280,
    0x8F77: 281,
    0x8F7B: 282,
    0x8FA1: 283,
    0x8FA2: 284,
    0x8FA5: 285,
    0x8FA9: 286,
    0x8FB1: 287,
    0x8FB3: 288,
    0x8FB5: 289,
    0x8FB7: 290,
    0x9061: 291,
    0x9062: 292,
    0x9063: 293,
    0x9065: 294,
    0x9068: 295,
    0x9069: 296,
    0x906A: 297,
    0x906B: 298,
    0x9071: 299,
    0x9073: 300,
    0x9075: 301,
    0x9076: 302,
    0x9077: 303,
    0x9078: 304,
    0x9079: 305,
    0x907B: 306,
    0x907D: 307,
    0x9081: 308,
    0x9082: 309,
    0x9085: 310,
    0x9089: 311,
    0x9091: 312,
    0x9093: 313,
    0x9095: 314,
    0x9096: 315,
    0x9097: 316,
    0x90A1: 317,
    0x90A2: 318,
    0x90A5: 319,
    0x90A9: 320,
    0x90B1: 321,
    0x90B7: 322,
    0x90E1: 323,
    0x90E2: 324,
    0x90E4: 325,
    0x90E5: 326,
    0x90E9: 327,
    0x90EB: 328,
    0x90EC: 329,
    0x90F1: 330,
    0x90F3: 331,
    0x90F5: 332,
    0x90F6: 333,
    0x90F7: 334,
    0x90FD: 335,
    0x9141: 336,
    0x9142: 337,
    0x9145: 338,
    0x9149: 339,
    0x9151: 340,
    0x9153: 341,
    0x9155: 342,
    0x9156: 343,
    0x9157: 344,
    0x9161: 345,
    0x9162: 346,
    0x9165: 347,
    0x9169: 348,
    0x9171: 349,
    0x9173: 350,
    0x9176: 351,
    0x9177: 352,
    0x917A: 353,
    0x9181: 354,
    0x9185: 355,
    0x91A1: 356,
    0x91A2: 357,
    0x91A5: 358,
    0x91A9: 359,
    0x91AB: 360,
    0x91B1: 361,
    0x91B3: 362,
    0x91B5: 363,
    0x91B7: 364,
    0x91BC: 365,
    0x91BD: 366,
    0x91C1: 367,
    0x91C5: 368,
    0x91C9: 369,
    0x91D6: 370,
    0x9241: 371,
    0x9245: 372,
    0x9249: 373,
    0x9251: 374,
    0x9253: 375,
    0x9255: 376,
    0x9261: 377,
    0x9262: 378,
    0x9265: 379,
    0x9269: 380,
    0x9273: 381,
    0x9275: 382,
    0x9277: 383,
    0x9281: 384,
    0x9282: 385,
    0x9285: 386,
    0x9288: 387,
    0x9289: 388,
    0x9291: 389,
    0x9293: 390,
    0x9295: 391,
    0x9297: 392,
    0x92A1: 393,
    0x92B6: 394,
    0x92C1: 395,
    0x92E1: 396,
    0x92E5: 397,
    0x92E9: 398,
    0x92F1: 399,
    0x92F3: 400,
    0x9341: 401,
    0x9342: 402,
    0x9349: 403,
    0x9351: 404,
    0x9353: 405,
    0x9357: 406,
    0x9361: 407,
    0x9362: 408,
    0x9365: 409,
    0x9369: 410,
    0x936A: 411,
    0x936B: 412,
    0x9371: 413,
    0x9373: 414,
    0x9375: 415,
    0x9377: 416,
    0x9378: 417,
    0x937C: 418,
    0x9381: 419,
    0x9385: 420,
    0x9389: 421,
    0x93A1: 422,
    0x93A2: 423,
    0x93A5: 424,
    0x93A9: 425,
    0x93AB: 426,
    0x93B1: 427,
    0x93B3: 428,
    0x93B5: 429,
    0x93B7: 430,
    0x93BC: 431,
    0x9461: 432,
    0x9462: 433,
    0x9463: 434,
    0x9465: 435,
    0x9468: 436,
    0x9469: 437,
    0x946A: 438,
    0x946B: 439,
    0x946C: 440,
    0x9470: 441,
    0x9471: 442,
    0x9473: 443,
    0x9475: 444,
    0x9476: 445,
    0x9477: 446,
    0x9478: 447,
    0x9479: 448,
    0x947D: 449,
    0x9481: 450,
    0x9482: 451,
    0x9485: 452,
    0x9489: 453,
    0x9491: 454,
    0x9493: 455,
    0x9495: 456,
    0x9496: 457,
    0x9497: 458,
    0x94A1: 459,
    0x94E1: 460,
    0x94E2: 461,
    0x94E3: 462,
    0x94E5: 463,
    0x94E8: 464,
    0x94E9: 465,
    0x94EB: 466,
    0x94EC: 467,
    0x94F1: 468,
    0x94F3: 469,
    0x94F5: 470,
    0x94F7: 471,
    0x94F9: 472,
    0x94FC: 473,
    0x9541: 474,
    0x9542: 475,
    0x9545: 476,
    0x9549: 477,
    0x9551: 478,
    0x9553: 479,
    0x9555: 480,
    0x9556: 481,
    0x9557: 482,
    0x9561: 483,
    0x9565: 484,
    0x9569: 485,
    0x9576: 486,
    0x9577: 487,
    0x9581: 488,
    0x9585: 489,
    0x95A1: 490,
    0x95A2: 491,
    0x95A5: 492,
    0x95A8: 493,
    0x95A9: 494,
    0x95AB: 495,
    0x95AD: 496,
    0x95B1: 497,
    0x95B3: 498,
    0x95B5: 499,
    0x95B7: 500,
    0x95B9: 501,
    0x95BB: 502,
    0x95C1: 503,
    0x95C5: 504,
    0x95C9: 505,
    0x95E1: 506,
    0x95F6: 507,
    0x9641: 508,
    0x9645: 509,
    0x9649: 510,
    0x9651: 511,
    0x9653: 512,
    0x9655: 513,
    0x9661: 514,
    0x9681: 515,
    0x9682: 516,
    0x9685: 517,
    0x9689: 518,
    0x9691: 519,
    0x9693: 520,
    0x9695: 521,
    0x9697: 522,
    0x96A1: 523,
    0x96B6: 524,
    0x96C1: 525,
    0x96D7: 526,
    0x96E1: 527,
    0x96E5: 528,
    0x96E9: 529,
    0x96F3: 530,
    0x96F5: 531,
    0x96F7: 532,
    0x9741: 533,
    0x9745: 534,
    0x9749: 535,
    0x9751: 536,
    0x9757: 537,
    0x9761: 538,
    0x9762: 539,
    0x9765: 540,
    0x9768: 541,
    0x9769: 542,
    0x976B: 543,
    0x9771: 544,
    0x9773: 545,
    0x9775: 546,
    0x9777: 547,
    0x9781: 548,
    0x97A1: 549,
    0x97A2: 550,
    0x97A5: 551,
    0x97A8: 552,
    0x97A9: 553,
    0x97B1: 554,
    0x97B3: 555,
    0x97B5: 556,
    0x97B6: 557,
    0x97B7: 558,
    0x97B8: 559,
    0x9861: 560,
    0x9862: 561,
    0x9865: 562,
    0x9869: 563,
    0x9871: 564,
    0x9873: 565,
    0x9875: 566,
    0x9876: 567,
    0x9877: 568,
    0x987D: 569,
    0x9881: 570,
    0x9882: 571,
    0x9885: 572,
    0x9889: 573,
    0x9891: 574,
    0x9893: 575,
    0x9895: 576,
    0x9896: 577,
    0x9897: 578,
    0x98E1: 579,
    0x98E2: 580,
    0x98E5: 581,
    0x98E9: 582,
    0x98EB: 583,
    0x98EC: 584,
    0x98F1: 585,
    0x98F3: 586,
    0x98F5: 587,
    0x98F6: 588,
    0x98F7: 589,
    0x98FD: 590,
    0x9941: 591,
    0x9942: 592,
    0x9945: 593,
    0x9949: 594,
    0x9951: 595,
    0x9953: 596,
    0x9955: 597,
    0x9956: 598,
    0x9957: 599,
    0x9961: 600,
    0x9976: 601,
    0x99A1: 602,
    0x99A2: 603,
    0x99A5: 604,
    0x99A9: 605,
    0x99B7: 606,
    0x99C1: 607,
    0x99C9: 608,
    0x99E1: 609,
    0x9A41: 610,
    0x9A45: 611,
    0x9A81: 612,
    0x9A82: 613,
    0x9A85: 614,
    0x9A89: 615,
    0x9A90: 616,
    0x9A91: 617,
    0x9A97: 618,
    0x9AC1: 619,
    0x9AE1: 620,
    0x9AE5: 621,
    0x9AE9: 622,
    0x9AF1: 623,
    0x9AF3: 624,
    0x9AF7: 625,
    0x9B61: 626,
    0x9B62: 627,
    0x9B65: 628,
    0x9B68: 629,
    0x9B69: 630,
    0x9B71: 631,
    0x9B73: 632,
    0x9B75: 633,
    0x9B81: 634,
    0x9B85: 635,
    0x9B89: 636,
    0x9B91: 637,
    0x9B93: 638,
    0x9BA1: 639,
    0x9BA5: 640,
    0x9BA9: 641,
    0x9BB1: 642,
    0x9BB3: 643,
    0x9BB5: 644,
    0x9BB7: 645,
    0x9C61: 646,
    0x9C62: 647,
    0x9C65: 648,
    0x9C69: 649,
    0x9C71: 650,
    0x9C73: 651,
    0x9C75: 652,
    0x9C76: 653,
    0x9C77: 654,
    0x9C78: 655,
    0x9C7C: 656,
    0x9C7D: 657,
    0x9C81: 658,
    0x9C82: 659,
    0x9C85: 660,
    0x9C89: 661,
    0x9C91: 662,
    0x9C93: 663,
    0x9C95: 664,
    0x9C96: 665,
    0x9C97: 666,
    0x9CA1: 667,
    0x9CA2: 668,
    0x9CA5: 669,
    0x9CB5: 670,
    0x9CB7: 671,
    0x9CE1: 672,
    0x9CE2: 673,
    0x9CE5: 674,
    0x9CE9: 675,
    0x9CF1: 676,
    0x9CF3: 677,
    0x9CF5: 678,
    0x9CF6: 679,
    0x9CF7: 680,
    0x9CFD: 681,
    0x9D41: 682,
    0x9D42: 683,
    0x9D45: 684,
    0x9D49: 685,
    0x9D51: 686,
    0x9D53: 687,
    0x9D55: 688,
    0x9D57: 689,
    0x9D61: 690,
    0x9D62: 691,
    0x9D65: 692,
    0x9D69: 693,
    0x9D71: 694,
    0x9D73: 695,
    0x9D75: 696,
    0x9D76: 697,
    0x9D77: 698,
    0x9D81: 699,
    0x9D85: 700,
    0x9D93: 701,
    0x9D95: 702,
    0x9DA1: 703,
    0x9DA2: 704,
    0x9DA5: 705,
    0x9DA9: 706,
    0x9DB1: 707,
    0x9DB3: 708,
    0x9DB5: 709,
    0x9DB7: 710,
    0x9DC1: 711,
    0x9DC5: 712,
    0x9DD7: 713,
    0x9DF6: 714,
    0x9E41: 715,
    0x9E45: 716,
    0x9E49: 717,
    0x9E51: 718,
    0x9E53: 719,
    0x9E55: 720,
    0x9E57: 721,
    0x9E61: 722,
    0x9E65: 723,
    0x9E69: 724,
    0x9E73: 725,
    0x9E75: 726,
    0x9E77: 727,
    0x9E81: 728,
    0x9E82: 729,
    0x9E85: 730,
    0x9E89: 731,
    0x9E91: 732,
    0x9E93: 733,
    0x9E95: 734,
    0x9E97: 735,
    0x9EA1: 736,
    0x9EB6: 737,
    0x9EC1: 738,
    0x9EE1: 739,
    0x9EE2: 740,
    0x9EE5: 741,
    0x9EE9: 742,
    0x9EF1: 743,
    0x9EF5: 744,
    0x9EF7: 745,
    0x9F41: 746,
    0x9F42: 747,
    0x9F45: 748,
    0x9F49: 749,
    0x9F51: 750,
    0x9F53: 751,
    0x9F55: 752,
    0x9F57: 753,
    0x9F61: 754,
    0x9F62: 755,
    0x9F65: 756,
    0x9F69: 757,
    0x9F71: 758,
    0x9F73: 759,
    0x9F75: 760,
    0x9F77: 761,
    0x9F78: 762,
    0x9F7B: 763,
    0x9F7C: 764,
    0x9FA1: 765,
    0x9FA2: 766,
    0x9FA5: 767,
    0x9FA9: 768,
    0x9FB1: 769,
    0x9FB3: 770,
    0x9FB5: 771,
    0x9FB7: 772,
    0xA061: 773,
    0xA062: 774,
    0xA065: 775,
    0xA067: 776,
    0xA068: 777,
    0xA069: 778,
    0xA06A: 779,
    0xA06B: 780,
    0xA071: 781,
    0xA073: 782,
    0xA075: 783,
    0xA077: 784,
    0xA078: 785,
    0xA07B: 786,
    0xA07D: 787,
    0xA081: 788,
    0xA082: 789,
    0xA085: 790,
    0xA089: 791,
    0xA091: 792,
    0xA093: 793,
    0xA095: 794,
    0xA096: 795,
    0xA097: 796,
    0xA098: 797,
    0xA0A1: 798,
    0xA0A2: 799,
    0xA0A9: 800,
    0xA0B7: 801,
    0xA0E1: 802,
    0xA0E2: 803,
    0xA0E5: 804,
    0xA0E9: 805,
    0xA0EB: 806,
    0xA0F1: 807,
    0xA0F3: 808,
    0xA0F5: 809,
    0xA0F7: 810,
    0xA0F8: 811,
    0xA0FD: 812,
    0xA141: 813,
    0xA142: 814,
    0xA145: 815,
    0xA149: 816,
    0xA151: 817,
    0xA153: 818,
    0xA155: 819,
    0xA156: 820,
    0xA157: 821,
    0xA161: 822,
    0xA162: 823,
    0xA165: 824,
    0xA169: 825,
    0xA175: 826,
    0xA176: 827,
    0xA177: 828,
    0xA179: 829,
    0xA181: 830,
    0xA1A1: 831,
    0xA1A2: 832,
    0xA1A4: 833,
    0xA1A5: 834,
    0xA1A9: 835,
    0xA1AB: 836,
    0xA1B1: 837,
    0xA1B3: 838,
    0xA1B5: 839,
    0xA1B7: 840,
    0xA1C1: 841,
    0xA1C5: 842,
    0xA1D6: 843,
    0xA1D7: 844,
    0xA241: 845,
    0xA245: 846,
    0xA249: 847,
    0xA253: 848,
    0xA255: 849,
    0xA257: 850,
    0xA261: 851,
    0xA265: 852,
    0xA269: 853,
    0xA273: 854,
    0xA275: 855,
    0xA281: 856,
    0xA282: 857,
    0xA283: 858,
    0xA285: 859,
    0xA288: 860,
    0xA289: 861,
    0xA28A: 862,
    0xA28B: 863,
    0xA291: 864,
    0xA293: 865,
    0xA295: 866,
    0xA297: 867,
    0xA29B: 868,
    0xA29D: 869,
    0xA2A1: 870,
    0xA2A5: 871,
    0xA2A9: 872,
    0xA2B3: 873,
    0xA2B5: 874,
    0xA2C1: 875,
    0xA2E1: 876,
    0xA2E5: 877,
    0xA2E9: 878,
    0xA341: 879,
    0xA345: 880,
    0xA349: 881,
    0xA351: 882,
    0xA355: 883,
    0xA361: 884,
    0xA365: 885,
    0xA369: 886,
    0xA371: 887,
    0xA375: 888,
    0xA3A1: 889,
    0xA3A2: 890,
    0xA3A5: 891,
    0xA3A8: 892,
    0xA3A9: 893,
    0xA3AB: 894,
    0xA3B1: 895,
    0xA3B3: 896,
    0xA3B5: 897,
    0xA3B6: 898,
    0xA3B7: 899,
    0xA3B9: 900,
    0xA3BB: 901,
    0xA461: 902,
    0xA462: 903,
    0xA463: 904,
    0xA464: 905,
    0xA465: 906,
    0xA468: 907,
    0xA469: 908,
    0xA46A: 909,
    0xA46B: 910,
    0xA46C: 911,
    0xA471: 912,
    0xA473: 913,
    0xA475: 914,
    0xA477: 915,
    0xA47B: 916,
    0xA481: 917,
    0xA482: 918,
    0xA485: 919,
    0xA489: 920,
    0xA491: 921,
    0xA493: 922,
    0xA495: 923,
    0xA496: 924,
    0xA497: 925,
    0xA49B: 926,
    0xA4A1: 927,
    0xA4A2: 928,
    0xA4A5: 929,
    0xA4B3: 930,
    0xA4E1: 931,
    0xA4E2: 932,
    0xA4E5: 933,
    0xA4E8: 934,
    0xA4E9: 935,
    0xA4EB: 936,
    0xA4F1: 937,
    0xA4F3: 938,
    0xA4F5: 939,
    0xA4F7: 940,
    0xA4F8: 941,
    0xA541: 942,
    0xA542: 943,
    0xA545: 944,
    0xA548: 945,
    0xA549: 946,
    0xA551: 947,
    0xA553: 948,
    0xA555: 949,
    0xA556: 950,
    0xA557: 951,
    0xA561: 952,
    0xA562: 953,
    0xA565: 954,
    0xA569: 955,
    0xA573: 956,
    0xA575: 957,
    0xA576: 958,
    0xA577: 959,
    0xA57B: 960,
    0xA581: 961,
    0xA585: 962,
    0xA5A1: 963,
    0xA5A2: 964,
    0xA5A3: 965,
    0xA5A5: 966,
    0xA5A9: 967,
    0xA5B1: 968,
    0xA5B3: 969,
    0xA5B5: 970,
    0xA5B7: 971,
    0xA5C1: 972,
    0xA5C5: 973,
    0xA5D6: 974,
    0xA5E1: 975,
    0xA5F6: 976,
    0xA641: 977,
    0xA642: 978,
    0xA645: 979,
    0xA649: 980,
    0xA651: 981,
    0xA653: 982,
    0xA661: 983,
    0xA665: 984,
    0xA681: 985,
    0xA682: 986,
    0xA685: 987,
    0xA688: 988,
    0xA689: 989,
    0xA68A: 990,
    0xA68B: 991,
    0xA691: 992,
    0xA693: 993,
    0xA695: 994,
    0xA697: 995,
    0xA69B: 996,
    0xA69C: 997,
    0xA6A1: 998,
    0xA6A9: 999,
    0xA6B6: 1000,
    0xA6C1: 1001,
    0xA6E1: 1002,
    0xA6E2: 1003,
    0xA6E5: 1004,
    0xA6E9: 1005,
    0xA6F7: 1006,
    0xA741: 1007,
    0xA745: 1008,
    0xA749: 1009,
    0xA751: 1010,
    0xA755: 1011,
    0xA757: 1012,
    0xA761: 1013,
    0xA762: 1014,
    0xA765: 1015,
    0xA769: 1016,
    0xA771: 1017,
    0xA773: 1018,
    0xA775: 1019,
    0xA7A1: 1020,
    0xA7A2: 1021,
    0xA7A5: 1022,
    0xA7A9: 1023,
    0xA7AB: 1024,
    0xA7B1: 1025,
    0xA7B3: 1026,
    0xA7B5: 1027,
    0xA7B7: 1028,
    0xA7B8: 1029,
    0xA7B9: 1030,
    0xA861: 1031,
    0xA862: 1032,
    0xA865: 1033,
    0xA869: 1034,
    0xA86B: 1035,
    0xA871: 1036,
    0xA873: 1037,
    0xA875: 1038,
    0xA876: 1039,
    0xA877: 1040,
    0xA87D: 1041,
    0xA881: 1042,
    0xA882: 1043,
    0xA885: 1044,
    0xA889: 1045,
    0xA891: 1046,
    0xA893: 1047,
    0xA895: 1048,
    0xA896: 1049,
    0xA897: 1050,
    0xA8A1: 1051,
    0xA8A2: 1052,
    0xA8B1: 1053,
    0xA8E1: 1054,
    0xA8E2: 1055,
    0xA8E5: 1056,
    0xA8E8: 1057,
    0xA8E9: 1058,
    0xA8F1: 1059,
    0xA8F5: 1060,
    0xA8F6: 1061,
    0xA8F7: 1062,
    0xA941: 1063,
    0xA957: 1064,
    0xA961: 1065,
    0xA962: 1066,
    0xA971: 1067,
    0xA973: 1068,
    0xA975: 1069,
    0xA976: 1070,
    0xA977: 1071,
    0xA9A1: 1072,
    0xA9A2: 1073,
    0xA9A5: 1074,
    0xA9A9: 1075,
    0xA9B1: 1076,
    0xA9B3: 1077,
    0xA9B7: 1078,
    0xAA41: 1079,
    0xAA61: 1080,
    0xAA77: 1081,
    0xAA81: 1082,
    0xAA82: 1083,
    0xAA85: 1084,
    0xAA89: 1085,
    0xAA91: 1086,
    0xAA95: 1087,
    0xAA97: 1088,
    0xAB41: 1089,
    0xAB57: 1090,
    0xAB61: 1091,
    0xAB65: 1092,
    0xAB69: 1093,
    0xAB71: 1094,
    0xAB73: 1095,
    0xABA1: 1096,
    0xABA2: 1097,
    0xABA5: 1098,
    0xABA9: 1099,
    0xABB1: 1100,
    0xABB3: 1101,
    0xABB5: 1102,
    0xABB7: 1103,
    0xAC61: 1104,
    0xAC62: 1105,
    0xAC64: 1106,
    0xAC65: 1107,
    0xAC68: 1108,
    0xAC69: 1109,
    0xAC6A: 1110,
    0xAC6B: 1111,
    0xAC71: 1112,
    0xAC73: 1113,
    0xAC75: 1114,
    0xAC76: 1115,
    0xAC77: 1116,
    0xAC7B: 1117,
    0xAC81: 1118,
    0xAC82: 1119,
    0xAC85: 1120,
    0xAC89: 1121,
    0xAC91: 1122,
    0xAC93: 1123,
    0xAC95: 1124,
    0xAC96: 1125,
    0xAC97: 1126,
    0xACA1: 1127,
    0xACA2: 1128,
    0xACA5: 1129,
    0xACA9: 1130,
    0xACB1: 1131,
    0xACB3: 1132,
    0xACB5: 1133,
    0xACB7: 1134,
    0xACC1: 1135,
    0xACC5: 1136,
    0xACC9: 1137,
    0xACD1: 1138,
    0xACD7: 1139,
    0xACE1: 1140,
    0xACE2: 1141,
    0xACE3: 1142,
    0xACE4: 1143,
    0xACE5: 1144,
    0xACE8: 1145,
    0xACE9: 1146,
    0xACEB: 1147,
    0xACEC: 1148,
    0xACF1: 1149,
    0xACF3: 1150,
    0xACF5: 1151,
    0xACF6: 1152,
    0xACF7: 1153,
    0xACFC: 1154,
    0xAD41: 1155,
    0xAD42: 1156,
    0xAD45: 1157,
    0xAD49: 1158,
    0xAD51: 1159,
    0xAD53: 1160,
    0xAD55: 1161,
    0xAD56: 1162,
    0xAD57: 1163,
    0xAD61: 1164,
    0xAD62: 1165,
    0xAD65: 1166,
    0xAD69: 1167,
    0xAD71: 1168,
    0xAD73: 1169,
    0xAD75: 1170,
    0xAD76: 1171,
    0xAD77: 1172,
    0xAD81: 1173,
    0xAD85: 1174,
    0xAD89: 1175,
    0xAD97: 1176,
    0xADA1: 1177,
    0xADA2: 1178,
    0xADA3: 1179,
    0xADA5: 1180,
    0xADA9: 1181,
    0xADAB: 1182,
    0xADB1: 1183,
    0xADB3: 1184,
    0xADB5: 1185,
    0xADB7: 1186,
    0xADBB: 1187,
    0xADC1: 1188,
    0xADC2: 1189,
    0xADC5: 1190,
    0xADC9: 1191,
    0xADD7: 1192,
    0xADE1: 1193,
    0xADE5: 1194,
    0xADE9: 1195,
    0xADF1: 1196,
    0xADF5: 1197,
    0xADF6: 1198,
    0xAE41: 1199,
    0xAE45: 1200,
    0xAE49: 1201,
    0xAE51: 1202,
    0xAE53: 1203,
    0xAE55: 1204,
    0xAE61: 1205,
    0xAE62: 1206,
    0xAE65: 1207,
    0xAE69: 1208,
    0xAE71: 1209,
    0xAE73: 1210,
    0xAE75: 1211,
    0xAE77: 1212,
    0xAE81: 1213,
    0xAE82: 1214,
    0xAE85: 1215,
    0xAE88: 1216,
    0xAE89: 1217,
    0xAE91: 1218,
    0xAE93: 1219,
    0xAE95: 1220,
    0xAE97: 1221,
    0xAE99: 1222,
    0xAE9B: 1223,
    0xAE9C: 1224,
    0xAEA1: 1225,
    0xAEB6: 1226,
    0xAEC1: 1227,
    0xAEC2: 1228,
    0xAEC5: 1229,
    0xAEC9: 1230,
    0xAED1: 1231,
    0xAED7: 1232,
    0xAEE1: 1233,
    0xAEE2: 1234,
    0xAEE5: 1235,
    0xAEE9: 1236,
    0xAEF1: 1237,
    0xAEF3: 1238,
    0xAEF5: 1239,
    0xAEF7: 1240,
    0xAF41: 1241,
    0xAF42: 1242,
    0xAF49: 1243,
    0xAF51: 1244,
    0xAF55: 1245,
    0xAF57: 1246,
    0xAF61: 1247,
    0xAF62: 1248,
    0xAF65: 1249,
    0xAF69: 1250,
    0xAF6A: 1251,
    0xAF71: 1252,
    0xAF73: 1253,
    0xAF75: 1254,
    0xAF77: 1255,
    0xAFA1: 1256,
    0xAFA2: 1257,
    0xAFA5: 1258,
    0xAFA8: 1259,
    0xAFA9: 1260,
    0xAFB0: 1261,
    0xAFB1: 1262,
    0xAFB3: 1263,
    0xAFB5: 1264,
    0xAFB7: 1265,
    0xAFBC: 1266,
    0xB061: 1267,
    0xB062: 1268,
    0xB064: 1269,
    0xB065: 1270,
    0xB069: 1271,
    0xB071: 1272,
    0xB073: 1273,
    0xB076: 1274,
    0xB077: 1275,
    0xB07D: 1276,
    0xB081: 1277,
    0xB082: 1278,
    0xB085: 1279,
    0xB089: 1280,
    0xB091: 1281,
    0xB093: 1282,
    0xB096: 1283,
    0xB097: 1284,
    0xB0B7: 1285,
    0xB0E1: 1286,
    0xB0E2: 1287,
    0xB0E5: 1288,
    0xB0E9: 1289,
    0xB0EB: 1290,
    0xB0F1: 1291,
    0xB0F3: 1292,
    0xB0F6: 1293,
    0xB0F7: 1294,
    0xB141: 1295,
    0xB145: 1296,
    0xB149: 1297,
    0xB185: 1298,
    0xB1A1: 1299,
    0xB1A2: 1300,
    0xB1A5: 1301,
    0xB1A8: 1302,
    0xB1A9: 1303,
    0xB1AB: 1304,
    0xB1B1: 1305,
    0xB1B3: 1306,
    0xB1B7: 1307,
    0xB1C1: 1308,
    0xB1C2: 1309,
    0xB1C5: 1310,
    0xB1D6: 1311,
    0xB1E1: 1312,
    0xB1F6: 1313,
    0xB241: 1314,
    0xB245: 1315,
    0xB249: 1316,
    0xB251: 1317,
    0xB253: 1318,
    0xB261: 1319,
    0xB281: 1320,
    0xB282: 1321,
    0xB285: 1322,
    0xB289: 1323,
    0xB291: 1324,
    0xB293: 1325,
    0xB297: 1326,
    0xB2A1: 1327,
    0xB2B6: 1328,
    0xB2C1: 1329,
    0xB2E1: 1330,
    0xB2E5: 1331,
    0xB357: 1332,
    0xB361: 1333,
    0xB362: 1334,
    0xB365: 1335,
    0xB369: 1336,
    0xB36B: 1337,
    0xB370: 1338,
    0xB371: 1339,
    0xB373: 1340,
    0xB381: 1341,
    0xB385: 1342,
    0xB389: 1343,
    0xB391: 1344,
    0xB3A1: 1345,
    0xB3A2: 1346,
    0xB3A5: 1347,
    0xB3A9: 1348,
    0xB3B1: 1349,
    0xB3B3: 1350,
    0xB3B5: 1351,
    0xB3B7: 1352,
    0xB461: 1353,
    0xB462: 1354,
    0xB465: 1355,
    0xB466: 1356,
    0xB467: 1357,
    0xB469: 1358,
    0xB46A: 1359,
    0xB46B: 1360,
    0xB470: 1361,
    0xB471: 1362,
    0xB473: 1363,
    0xB475: 1364,
    0xB476: 1365,
    0xB477: 1366,
    0xB47B: 1367,
    0xB47C: 1368,
    0xB481: 1369,
    0xB482: 1370,
    0xB485: 1371,
    0xB489: 1372,
    0xB491: 1373,
    0xB493: 1374,
    0xB495: 1375,
    0xB496: 1376,
    0xB497: 1377,
    0xB4A1: 1378,
    0xB4A2: 1379,
    0xB4A5: 1380,
    0xB4A9: 1381,
    0xB4AC: 1382,
    0xB4B1: 1383,
    0xB4B3: 1384,
    0xB4B5: 1385,
    0xB4B7: 1386,
    0xB4BB: 1387,
    0xB4BD: 1388,
    0xB4C1: 1389,
    0xB4C5: 1390,
    0xB4C9: 1391,
    0xB4D3: 1392,
    0xB4E1: 1393,
    0xB4E2: 1394,
    0xB4E5: 1395,
    0xB4E6: 1396,
    0xB4E8: 1397,
    0xB4E9: 1398,
    0xB4EA: 1399,
    0xB4EB: 1400,
    0xB4F1: 1401,
    0xB4F3: 1402,
    0xB4F4: 1403,
    0xB4F5: 1404,
    0xB4F6: 1405,
    0xB4F7: 1406,
    0xB4F8: 1407,
    0xB4FA: 1408,
    0xB4FC: 1409,
    0xB541: 1410,
    0xB542: 1411,
    0xB545: 1412,
    0xB549: 1413,
    0xB551: 1414,
    0xB553: 1415,
    0xB555: 1416,
    0xB557: 1417,
    0xB561: 1418,
    0xB562: 1419,
    0xB563: 1420,
    0xB565: 1421,
    0xB569: 1422,
    0xB56B: 1423,
    0xB56C: 1424,
    0xB571: 1425,
    0xB573: 1426,
    0xB574: 1427,
    0xB575: 1428,
    0xB576: 1429,
    0xB577: 1430,
    0xB57B: 1431,
    0xB57C: 1432,
    0xB57D: 1433,
    0xB581: 1434,
    0xB585: 1435,
    0xB589: 1436,
    0xB591: 1437,
    0xB593: 1438,
    0xB595: 1439,
    0xB596: 1440,
    0xB5A1: 1441,
    0xB5A2: 1442,
    0xB5A5: 1443,
    0xB5A9: 1444,
    0xB5AA: 1445,
    0xB5AB: 1446,
    0xB5AD: 1447,
    0xB5B0: 1448,
    0xB5B1: 1449,
    0xB5B3: 1450,
    0xB5B5: 1451,
    0xB5B7: 1452,
    0xB5B9: 1453,
    0xB5C1: 1454,
    0xB5C2: 1455,
    0xB5C5: 1456,
    0xB5C9: 1457,
    0xB5D1: 1458,
    0xB5D3: 1459,
    0xB5D5: 1460,
    0xB5D6: 1461,
    0xB5D7: 1462,
    0xB5E1: 1463,
    0xB5E2: 1464,
    0xB5E5: 1465,
    0xB5F1: 1466,
    0xB5F5: 1467,
    0xB5F7: 1468,
    0xB641: 1469,
    0xB642: 1470,
    0xB645: 1471,
    0xB649: 1472,
    0xB651: 1473,
    0xB653: 1474,
    0xB655: 1475,
    0xB657: 1476,
    0xB661: 1477,
    0xB662: 1478,
    0xB665: 1479,
    0xB669: 1480,
    0xB671: 1481,
    0xB673: 1482,
    0xB675: 1483,
    0xB677: 1484,
    0xB681: 1485,
    0xB682: 1486,
    0xB685: 1487,
    0xB689: 1488,
    0xB68A: 1489,
    0xB68B: 1490,
    0xB691: 1491,
    0xB693: 1492,
    0xB695: 1493,
    0xB697: 1494,
    0xB6A1: 1495,
    0xB6A2: 1496,
    0xB6A5: 1497,
    0xB6A9: 1498,
    0xB6B1: 1499,
    0xB6B3: 1500,
    0xB6B6: 1501,
    0xB6B7: 1502,
    0xB6C1: 1503,
    0xB6C2: 1504,
    0xB6C5: 1505,
    0xB6C9: 1506,
    0xB6D1: 1507,
    0xB6D3: 1508,
    0xB6D7: 1509,
    0xB6E1: 1510,
    0xB6E2: 1511,
    0xB6E5: 1512,
    0xB6E9: 1513,
    0xB6F1: 1514,
    0xB6F3: 1515,
    0xB6F5: 1516,
    0xB6F7: 1517,
    0xB741: 1518,
    0xB742: 1519,
    0xB745: 1520,
    0xB749: 1521,
    0xB751: 1522,
    0xB753: 1523,
    0xB755: 1524,
    0xB757: 1525,
    0xB759: 1526,
    0xB761: 1527,
    0xB762: 1528,
    0xB765: 1529,
    0xB769: 1530,
    0xB76F: 1531,
    0xB771: 1532,
    0xB773: 1533,
    0xB775: 1534,
    0xB777: 1535,
    0xB778: 1536,
    0xB779: 1537,
    0xB77A: 1538,
    0xB77B: 1539,
    0xB77C: 1540,
    0xB77D: 1541,
    0xB781: 1542,
    0xB785: 1543,
    0xB789: 1544,
    0xB791: 1545,
    0xB795: 1546,
    0xB7A1: 1547,
    0xB7A2: 1548,
    0xB7A5: 1549,
    0xB7A9: 1550,
    0xB7AA: 1551,
    0xB7AB: 1552,
    0xB7B0: 1553,
    0xB7B1: 1554,
    0xB7B3: 1555,
    0xB7B5: 1556,
    0xB7B6: 1557,
    0xB7B7: 1558,
    0xB7B8: 1559,
    0xB7BC: 1560,
    0xB861: 1561,
    0xB862: 1562,
    0xB865: 1563,
    0xB867: 1564,
    0xB868: 1565,
    0xB869: 1566,
    0xB86B: 1567,
    0xB871: 1568,
    0xB873: 1569,
    0xB875: 1570,
    0xB876: 1571,
    0xB877: 1572,
    0xB878: 1573,
    0xB881: 1574,
    0xB882: 1575,
    0xB885: 1576,
    0xB889: 1577,
    0xB891: 1578,
    0xB893: 1579,
    0xB895: 1580,
    0xB896: 1581,
    0xB897: 1582,
    0xB8A1: 1583,
    0xB8A2: 1584,
    0xB8A5: 1585,
    0xB8A7: 1586,
    0xB8A9: 1587,
    0xB8B1: 1588,
    0xB8B7: 1589,
    0xB8C1: 1590,
    0xB8C5: 1591,
    0xB8C9: 1592,
    0xB8E1: 1593,
    0xB8E2: 1594,
    0xB8E5: 1595,
    0xB8E9: 1596,
    0xB8EB: 1597,
    0xB8F1: 1598,
    0xB8F3: 1599,
    0xB8F5: 1600,
    0xB8F7: 1601,
    0xB8F8: 1602,
    0xB941: 1603,
    0xB942: 1604,
    0xB945: 1605,
    0xB949: 1606,
    0xB951: 1607,
    0xB953: 1608,
    0xB955: 1609,
    0xB957: 1610,
    0xB961: 1611,
    0xB965: 1612,
    0xB969: 1613,
    0xB971: 1614,
    0xB973: 1615,
    0xB976: 1616,
    0xB977: 1617,
    0xB981: 1618,
    0xB9A1: 1619,
    0xB9A2: 1620,
    0xB9A5: 1621,
    0xB9A9: 1622,
    0xB9AB: 1623,
    0xB9B1: 1624,
    0xB9B3: 1625,
    0xB9B5: 1626,
    0xB9B7: 1627,
    0xB9B8: 1628,
    0xB9B9: 1629,
    0xB9BD: 1630,
    0xB9C1: 1631,
    0xB9C2: 1632,
    0xB9C9: 1633,
    0xB9D3: 1634,
    0xB9D5: 1635,
    0xB9D7: 1636,
    0xB9E1: 1637,
    0xB9F6: 1638,
    0xB9F7: 1639,
    0xBA41: 1640,
    0xBA45: 1641,
    0xBA49: 1642,
    0xBA51: 1643,
    0xBA53: 1644,
    0xBA55: 1645,
    0xBA57: 1646,
    0xBA61: 1647,
    0xBA62: 1648,
    0xBA65: 1649,
    0xBA77: 1650,
    0xBA81: 1651,
    0xBA82: 1652,
    0xBA85: 1653,
    0xBA89: 1654,
    0xBA8A: 1655,
    0xBA8B: 1656,
    0xBA91: 1657,
    0xBA93: 1658,
    0xBA95: 1659,
    0xBA97: 1660,
    0xBAA1: 1661,
    0xBAB6: 1662,
    0xBAC1: 1663,
    0xBAE1: 1664,
    0xBAE2: 1665,
    0xBAE5: 1666,
    0xBAE9: 1667,
    0xBAF1: 1668,
    0xBAF3: 1669,
    0xBAF5: 1670,
    0xBB41: 1671,
    0xBB45: 1672,
    0xBB49: 1673,
    0xBB51: 1674,
    0xBB61: 1675,
    0xBB62: 1676,
    0xBB65: 1677,
    0xBB69: 1678,
    0xBB71: 1679,
    0xBB73: 1680,
    0xBB75: 1681,
    0xBB77: 1682,
    0xBBA1: 1683,
    0xBBA2: 1684,
    0xBBA5: 1685,
    0xBBA8: 1686,
    0xBBA9: 1687,
    0xBBAB: 1688,
    0xBBB1: 1689,
    0xBBB3: 1690,
    0xBBB5: 1691,
    0xBBB7: 1692,
    0xBBB8: 1693,
    0xBBBB: 1694,
    0xBBBC: 1695,
    0xBC61: 1696,
    0xBC62: 1697,
    0xBC65: 1698,
    0xBC67: 1699,
    0xBC69: 1700,
    0xBC6C: 1701,
    0xBC71: 1702,
    0xBC73: 1703,
    0xBC75: 1704,
    0xBC76: 1705,
    0xBC77: 1706,
    0xBC81: 1707,
    0xBC82: 1708,
    0xBC85: 1709,
    0xBC89: 1710,
    0xBC91: 1711,
    0xBC93: 1712,
    0xBC95: 1713,
    0xBC96: 1714,
    0xBC97: 1715,
    0xBCA1: 1716,
    0xBCA5: 1717,
    0xBCB7: 1718,
    0xBCE1: 1719,
    0xBCE2: 1720,
    0xBCE5: 1721,
    0xBCE9: 1722,
    0xBCF1: 1723,
    0xBCF3: 1724,
    0xBCF5: 1725,
    0xBCF6: 1726,
    0xBCF7: 1727,
    0xBD41: 1728,
    0xBD57: 1729,
    0xBD61: 1730,
    0xBD76: 1731,
    0xBDA1: 1732,
    0xBDA2: 1733,
    0xBDA5: 1734,
    0xBDA9: 1735,
    0xBDB1: 1736,
    0xBDB3: 1737,
    0xBDB5: 1738,
    0xBDB7: 1739,
    0xBDB9: 1740,
    0xBDC1: 1741,
    0xBDC2: 1742,
    0xBDC9: 1743,
    0xBDD6: 1744,
    0xBDE1: 1745,
    0xBDF6: 1746,
    0xBE41: 1747,
    0xBE45: 1748,
    0xBE49: 1749,
    0xBE51: 1750,
    0xBE53: 1751,
    0xBE77: 1752,
    0xBE81: 1753,
    0xBE82: 1754,
    0xBE85: 1755,
    0xBE89: 1756,
    0xBE91: 1757,
    0xBE93: 1758,
    0xBE97: 1759,
    0xBEA1: 1760,
    0xBEB6: 1761,
    0xBEB7: 1762,
    0xBEE1: 1763,
    0xBF41: 1764,
    0xBF61: 1765,
    0xBF71: 1766,
    0xBF75: 1767,
    0xBF77: 1768,
    0xBFA1: 1769,
    0xBFA2: 1770,
    0xBFA5: 1771,
    0xBFA9: 1772,
    0xBFB1: 1773,
    0xBFB3: 1774,
    0xBFB7: 1775,
    0xBFB8: 1776,
    0xBFBD: 1777,
    0xC061: 1778,
    0xC062: 1779,
    0xC065: 1780,
    0xC067: 1781,
    0xC069: 1782,
    0xC071: 1783,
    0xC073: 1784,
    0xC075: 1785,
    0xC076: 1786,
    0xC077: 1787,
    0xC078: 1788,
    0xC081: 1789,
    0xC082: 1790,
    0xC085: 1791,
    0xC089: 1792,
    0xC091: 1793,
    0xC093: 1794,
    0xC095: 1795,
    0xC096: 1796,
    0xC097: 1797,
    0xC0A1: 1798,
    0xC0A5: 1799,
    0xC0A7: 1800,
    0xC0A9: 1801,
    0xC0B1: 1802,
    0xC0B7: 1803,
    0xC0E1: 1804,
    0xC0E2: 1805,
    0xC0E5: 1806,
    0xC0E9: 1807,
    0xC0F1: 1808,
    0xC0F3: 1809,
    0xC0F5: 1810,
    0xC0F6: 1811,
    0xC0F7: 1812,
    0xC141: 1813,
    0xC142: 1814,
    0xC145: 1815,
    0xC149: 1816,
    0xC151: 1817,
    0xC153: 1818,
    0xC155: 1819,
    0xC157: 1820,
    0xC161: 1821,
    0xC165: 1822,
    0xC176: 1823,
    0xC181: 1824,
    0xC185: 1825,
    0xC197: 1826,
    0xC1A1: 1827,
    0xC1A2: 1828,
    0xC1A5: 1829,
    0xC1A9: 1830,
    0xC1B1: 1831,
    0xC1B3: 1832,
    0xC1B5: 1833,
    0xC1B7: 1834,
    0xC1C1: 1835,
    0xC1C5: 1836,
    0xC1C9: 1837,
    0xC1D7: 1838,
    0xC241: 1839,
    0xC245: 1840,
    0xC249: 1841,
    0xC251: 1842,
    0xC253: 1843,
    0xC255: 1844,
    0xC257: 1845,
    0xC261: 1846,
    0xC271: 1847,
    0xC281: 1848,
    0xC282: 1849,
    0xC285: 1850,
    0xC289: 1851,
    0xC291: 1852,
    0xC293: 1853,
    0xC295: 1854,
    0xC297: 1855,
    0xC2A1: 1856,
    0xC2B6: 1857,
    0xC2C1: 1858,
    0xC2C5: 1859,
    0xC2E1: 1860,
    0xC2E5: 1861,
    0xC2E9: 1862,
    0xC2F1: 1863,
    0xC2F3: 1864,
    0xC2F5: 1865,
    0xC2F7: 1866,
    0xC341: 1867,
    0xC345: 1868,
    0xC349: 1869,
    0xC351: 1870,
    0xC357: 1871,
    0xC361: 1872,
    0xC362: 1873,
    0xC365: 1874,
    0xC369: 1875,
    0xC371: 1876,
    0xC373: 1877,
    0xC375: 1878,
    0xC377: 1879,
    0xC3A1: 1880,
    0xC3A2: 1881,
    0xC3A5: 1882,
    0xC3A8: 1883,
    0xC3A9: 1884,
    0xC3AA: 1885,
    0xC3B1: 1886,
    0xC3B3: 1887,
    0xC3B5: 1888,
    0xC3B7: 1889,
    0xC461: 1890,
    0xC462: 1891,
    0xC465: 1892,
    0xC469: 1893,
    0xC471: 1894,
    0xC473: 1895,
    0xC475: 1896,
    0xC477: 1897,
    0xC481: 1898,
    0xC482: 1899,
    0xC485: 1900,
    0xC489: 1901,
    0xC491: 1902,
    0xC493: 1903,
    0xC495: 1904,
    0xC496: 1905,
    0xC497: 1906,
    0xC4A1: 1907,
    0xC4A2: 1908,
    0xC4B7: 1909,
    0xC4E1: 1910,
    0xC4E2: 1911,
    0xC4E5: 1912,
    0xC4E8: 1913,
    0xC4E9: 1914,
    0xC4F1: 1915,
    0xC4F3: 1916,
    0xC4F5: 1917,
    0xC4F6: 1918,
    0xC4F7: 1919,
    0xC541: 1920,
    0xC542: 1921,
    0xC545: 1922,
    0xC549: 1923,
    0xC551: 1924,
    0xC553: 1925,
    0xC555: 1926,
    0xC557: 1927,
    0xC561: 1928,
    0xC565: 1929,
    0xC569: 1930,
    0xC571: 1931,
    0xC573: 1932,
    0xC575: 1933,
    0xC576: 1934,
    0xC577: 1935,
    0xC581: 1936,
    0xC5A1: 1937,
    0xC5A2: 1938,
    0xC5A5: 1939,
    0xC5A9: 1940,
    0xC5B1: 1941,
    0xC5B3: 1942,
    0xC5B5: 1943,
    0xC5B7: 1944,
    0xC5C1: 1945,
    0xC5C2: 1946,
    0xC5C5: 1947,
    0xC5C9: 1948,
    0xC5D1: 1949,
    0xC5D7: 1950,
    0xC5E1: 1951,
    0xC5F7: 1952,
    0xC641: 1953,
    0xC649: 1954,
    0xC661: 1955,
    0xC681: 1956,
    0xC682: 1957,
    0xC685: 1958,
    0xC689: 1959,
    0xC691: 1960,
    0xC693: 1961,
    0xC695: 1962,
    0xC697: 1963,
    0xC6A1: 1964,
    0xC6A5: 1965,
    0xC6A9: 1966,
    0xC6B7: 1967,
    0xC6C1: 1968,
    0xC6D7: 1969,
    0xC6E1: 1970,
    0xC6E2: 1971,
    0xC6E5: 1972,
    0xC6E9: 1973,
    0xC6F1: 1974,
    0xC6F3: 1975,
    0xC6F5: 1976,
    0xC6F7: 1977,
    0xC741: 1978,
    0xC745: 1979,
    0xC749: 1980,
    0xC751: 1981,
    0xC761: 1982,
    0xC762: 1983,
    0xC765: 1984,
    0xC769: 1985,
    0xC771: 1986,
    0xC773: 1987,
    0xC777: 1988,
    0xC7A1: 1989,
    0xC7A2: 1990,
    0xC7A5: 1991,
    0xC7A9: 1992,
    0xC7B1: 1993,
    0xC7B3: 1994,
    0xC7B5: 1995,
    0xC7B7: 1996,
    0xC861: 1997,
    0xC862: 1998,
    0xC865: 1999,
    0xC869: 2000,
    0xC86A: 2001,
    0xC871: 2002,
    0xC873: 2003,
    0xC875: 2004,
    0xC876: 2005,
    0xC877: 2006,
    0xC881: 2007,
    0xC882: 2008,
    0xC885: 2009,
    0xC889: 2010,
    0xC891: 2011,
    0xC893: 2012,
    0xC895: 2013,
    0xC896: 2014,
    0xC897: 2015,
    0xC8A1: 2016,
    0xC8B7: 2017,
    0xC8E1: 2018,
    0xC8E2: 2019,
    0xC8E5: 2020,
    0xC8E9: 2021,
    0xC8EB: 2022,
    0xC8F1: 2023,
    0xC8F3: 2024,
    0xC8F5: 2025,
    0xC8F6: 2026,
    0xC8F7: 2027,
    0xC941: 2028,
    0xC942: 2029,
    0xC945: 2030,
    0xC949: 2031,
    0xC951: 2032,
    0xC953: 2033,
    0xC955: 2034,
    0xC957: 2035,
    0xC961: 2036,
    0xC965: 2037,
    0xC976: 2038,
    0xC981: 2039,
    0xC985: 2040,
    0xC9A1: 2041,
    0xC9A2: 2042,
    0xC9A5: 2043,
    0xC9A9: 2044,
    0xC9B1: 2045,
    0xC9B3: 2046,
    0xC9B5: 2047,
    0xC9B7: 2048,
    0xC9BC: 2049,
    0xC9C1: 2050,
    0xC9C5: 2051,
    0xC9E1: 2052,
    0xCA41: 2053,
    0xCA45: 2054,
    0xCA55: 2055,
    0xCA57: 2056,
    0xCA61: 2057,
    0xCA81: 2058,
    0xCA82: 2059,
    0xCA85: 2060,
    0xCA89: 2061,
    0xCA91: 2062,
    0xCA93: 2063,
    0xCA95: 2064,
    0xCA97: 2065,
    0xCAA1: 2066,
    0xCAB6: 2067,
    0xCAC1: 2068,
    0xCAE1: 2069,
    0xCAE2: 2070,
    0xCAE5: 2071,
    0xCAE9: 2072,
    0xCAF1: 2073,
    0xCAF3: 2074,
    0xCAF7: 2075,
    0xCB41: 2076,
    0xCB45: 2077,
    0xCB49: 2078,
    0xCB51: 2079,
    0xCB57: 2080,
    0xCB61: 2081,
    0xCB62: 2082,
    0xCB65: 2083,
    0xCB68: 2084,
    0xCB69: 2085,
    0xCB6B: 2086,
    0xCB71: 2087,
    0xCB73: 2088,
    0xCB75: 2089,
    0xCB81: 2090,
    0xCB85: 2091,
    0xCB89: 2092,
    0xCB91: 2093,
    0xCB93: 2094,
    0xCBA1: 2095,
    0xCBA2: 2096,
    0xCBA5: 2097,
    0xCBA9: 2098,
    0xCBB1: 2099,
    0xCBB3: 2100,
    0xCBB5: 2101,
    0xCBB7: 2102,
    0xCC61: 2103,
    0xCC62: 2104,
    0xCC63: 2105,
    0xCC65: 2106,
    0xCC69: 2107,
    0xCC6B: 2108,
    0xCC71: 2109,
    0xCC73: 2110,
    0xCC75: 2111,
    0xCC76: 2112,
    0xCC77: 2113,
    0xCC7B: 2114,
    0xCC81: 2115,
    0xCC82: 2116,
    0xCC85: 2117,
    0xCC89: 2118,
    0xCC91: 2119,
    0xCC93: 2120,
    0xCC95: 2121,
    0xCC96: 2122,
    0xCC97: 2123,
    0xCCA1: 2124,
    0xCCA2: 2125,
    0xCCE1: 2126,
    0xCCE2: 2127,
    0xCCE5: 2128,
    0xCCE9: 2129,
    0xCCF1: 2130,
    0xCCF3: 2131,
    0xCCF5: 2132,
    0xCCF6: 2133,
    0xCCF7: 2134,
    0xCD41: 2135,
    0xCD42: 2136,
    0xCD45: 2137,
    0xCD49: 2138,
    0xCD51: 2139,
    0xCD53: 2140,
    0xCD55: 2141,
    0xCD57: 2142,
    0xCD61: 2143,
    0xCD65: 2144,
    0xCD69: 2145,
    0xCD71: 2146,
    0xCD73: 2147,
    0xCD76: 2148,
    0xCD77: 2149,
    0xCD81: 2150,
    0xCD89: 2151,
    0xCD93: 2152,
    0xCD95: 2153,
    0xCDA1: 2154,
    0xCDA2: 2155,
    0xCDA5: 2156,
    0xCDA9: 2157,
    0xCDB1: 2158,
    0xCDB3: 2159,
    0xCDB5: 2160,
    0xCDB7: 2161,
    0xCDC1: 2162,
    0xCDD7: 2163,
    0xCE41: 2164,
    0xCE45: 2165,
    0xCE61: 2166,
    0xCE65: 2167,
    0xCE69: 2168,
    0xCE73: 2169,
    0xCE75: 2170,
    0xCE81: 2171,
    0xCE82: 2172,
    0xCE85: 2173,
    0xCE88: 2174,
    0xCE89: 2175,
    0xCE8B: 2176,
    0xCE91: 2177,
    0xCE93: 2178,
    0xCE95: 2179,
    0xCE97: 2180,
    0xCEA1: 2181,
    0xCEB7: 2182,
    0xCEE1: 2183,
    0xCEE5: 2184,
    0xCEE9: 2185,
    0xCEF1: 2186,
    0xCEF5: 2187,
    0xCF41: 2188,
    0xCF45: 2189,
    0xCF49: 2190,
    0xCF51: 2191,
    0xCF55: 2192,
    0xCF57: 2193,
    0xCF61: 2194,
    0xCF65: 2195,
    0xCF69: 2196,
    0xCF71: 2197,
    0xCF73: 2198,
    0xCF75: 2199,
    0xCFA1: 2200,
    0xCFA2: 2201,
    0xCFA5: 2202,
    0xCFA9: 2203,
    0xCFB1: 2204,
    0xCFB3: 2205,
    0xCFB5: 2206,
    0xCFB7: 2207,
    0xD061: 2208,
    0xD062: 2209,
    0xD065: 2210,
    0xD069: 2211,
    0xD06E: 2212,
    0xD071: 2213,
    0xD073: 2214,
    0xD075: 2215,
    0xD077: 2216,
    0xD081: 2217,
    0xD082: 2218,
    0xD085: 2219,
    0xD089: 2220,
    0xD091: 2221,
    0xD093: 2222,
    0xD095: 2223,
    0xD096: 2224,
    0xD097: 2225,
    0xD0A1: 2226,
    0xD0B7: 2227,
    0xD0E1: 2228,
    0xD0E2: 2229,
    0xD0E5: 2230,
    0xD0E9: 2231,
    0xD0EB: 2232,
    0xD0F1: 2233,
    0xD0F3: 2234,
    0xD0F5: 2235,
    0xD0F7: 2236,
    0xD141: 2237,
    0xD142: 2238,
    0xD145: 2239,
    0xD149: 2240,
    0xD151: 2241,
    0xD153: 2242,
    0xD155: 2243,
    0xD157: 2244,
    0xD161: 2245,
    0xD162: 2246,
    0xD165: 2247,
    0xD169: 2248,
    0xD171: 2249,
    0xD173: 2250,
    0xD175: 2251,
    0xD176: 2252,
    0xD177: 2253,
    0xD181: 2254,
    0xD185: 2255,
    0xD189: 2256,
    0xD193: 2257,
    0xD1A1: 2258,
    0xD1A2: 2259,
    0xD1A5: 2260,
    0xD1A9: 2261,
    0xD1AE: 2262,
    0xD1B1: 2263,
    0xD1B3: 2264,
    0xD1B5: 2265,
    0xD1B7: 2266,
    0xD1BB: 2267,
    0xD1C1: 2268,
    0xD1C2: 2269,
    0xD1C5: 2270,
    0xD1C9: 2271,
    0xD1D5: 2272,
    0xD1D7: 2273,
    0xD1E1: 2274,
    0xD1E2: 2275,
    0xD1E5: 2276,
    0xD1F5: 2277,
    0xD1F7: 2278,
    0xD241: 2279,
    0xD242: 2280,
    0xD245: 2281,
    0xD249: 2282,
    0xD253: 2283,
    0xD255: 2284,
    0xD257: 2285,
    0xD261: 2286,
    0xD265: 2287,
    0xD269: 2288,
    0xD273: 2289,
    0xD275: 2290,
    0xD281: 2291,
    0xD282: 2292,
    0xD285: 2293,
    0xD289: 2294,
    0xD28E: 2295,
    0xD291: 2296,
    0xD295: 2297,
    0xD297: 2298,
    0xD2A1: 2299,
    0xD2A5: 2300,
    0xD2A9: 2301,
    0xD2B1: 2302,
    0xD2B7: 2303,
    0xD2C1: 2304,
    0xD2C2: 2305,
    0xD2C5: 2306,
    0xD2C9: 2307,
    0xD2D7: 2308,
    0xD2E1: 2309,
    0xD2E2: 2310,
    0xD2E5: 2311,
    0xD2E9: 2312,
    0xD2F1: 2313,
    0xD2F3: 2314,
    0xD2F5: 2315,
    0xD2F7: 2316,
    0xD341: 2317,
    0xD342: 2318,
    0xD345: 2319,
    0xD349: 2320,
    0xD351: 2321,
    0xD355: 2322,
    0xD357: 2323,
    0xD361: 2324,
    0xD362: 2325,
    0xD365: 2326,
    0xD367: 2327,
    0xD368: 2328,
    0xD369: 2329,
    0xD36A: 2330,
    0xD371: 2331,
    0xD373: 2332,
    0xD375: 2333,
    0xD377: 2334,
    0xD37B: 2335,
    0xD381: 2336,
    0xD385: 2337,
    0xD389: 2338,
    0xD391: 2339,
    0xD393: 2340,
    0xD397: 2341,
    0xD3A1: 2342,
    0xD3A2: 2343,
    0xD3A5: 2344,
    0xD3A9: 2345,
    0xD3B1: 2346,
    0xD3B3: 2347,
    0xD3B5: 2348,
    0xD3B7: 2349,
}
